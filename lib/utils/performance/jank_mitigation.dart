import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/scheduler.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/widget_optimizer.dart';

/// Comprehensive jank mitigation system for Dasso Reader
class JankMitigationSystem {
  static final JankMitigationSystem _instance =
      JankMitigationSystem._internal();
  factory JankMitigationSystem() => _instance;
  JankMitigationSystem._internal();

  bool _isInitialized = false;
  final List<JankMitigationStrategy> _strategies = [];
  Timer? _monitoringTimer;

  /// Initialize the jank mitigation system
  Future<void> initialize() async {
    if (_isInitialized) return;

    // TEMPORARILY DISABLED FOR iOS DEBUGGING - CLEANER LOGS
    // AnxLog.info('🛡️ JankMitigationSystem: Initializing');

    // Register mitigation strategies
    _registerStrategies();

    // Start monitoring
    _startMonitoring();

    _isInitialized = true;

    // TEMPORARILY DISABLED FOR iOS DEBUGGING - CLEANER LOGS
    // AnxLog.info(
    //   '🛡️ JankMitigationSystem: Initialized with ${_strategies.length} strategies',
    // );
  }

  /// Register all jank mitigation strategies
  void _registerStrategies() {
    _strategies.addAll([
      StartupJankMitigation(),
      SetStateDuringBuildMitigation(),
      DictionaryLoadingMitigation(),
      MemorySpikeMitigation(),
      WidgetRebuildMitigation(),
      DatabaseOperationMitigation(),
    ]);
  }

  /// Start monitoring for jank patterns
  void _startMonitoring() {
    // TEMPORARILY DISABLED FOR iOS DEBUGGING - CLEANER LOGS
    // _monitoringTimer = Timer.periodic(const Duration(seconds: 10), (_) {
    //   _analyzeAndMitigate();
    // });
  }

  /// Analyze current performance and apply mitigations
  void _analyzeAndMitigate() {
    for (final strategy in _strategies) {
      if (strategy.shouldApply()) {
        strategy.apply();
      }
    }
  }

  /// Apply specific mitigation for detected jank type
  void mitigateJank(JankType type, Map<String, dynamic> context) {
    final applicableStrategies = _strategies.where((s) => s.canHandle(type));

    for (final strategy in applicableStrategies) {
      strategy.applyWithContext(context);
    }
  }

  /// Dispose resources
  void dispose() {
    _monitoringTimer?.cancel();
    for (final strategy in _strategies) {
      strategy.dispose();
    }
    _strategies.clear();
    _isInitialized = false;
  }
}

/// Base class for jank mitigation strategies
abstract class JankMitigationStrategy {
  const JankMitigationStrategy({
    required this.name,
    required this.targetJankTypes,
  });

  final String name;
  final List<JankType> targetJankTypes;

  /// Check if this strategy can handle the given jank type
  bool canHandle(JankType type) => targetJankTypes.contains(type);

  /// Check if this strategy should be applied
  bool shouldApply();

  /// Apply the mitigation strategy
  void apply();

  /// Apply with specific context
  void applyWithContext(Map<String, dynamic> context) => apply();

  /// Dispose resources
  void dispose() {}
}

/// Mitigation for startup jank
class StartupJankMitigation extends JankMitigationStrategy {
  StartupJankMitigation()
      : super(
          name: 'Startup Jank Mitigation',
          targetJankTypes: [JankType.startup, JankType.initialization],
        );

  @override
  bool shouldApply() {
    // Apply during app startup phase
    return true; // This would be more sophisticated in practice
  }

  @override
  void apply() {
    // Disabled for cleaner development experience
    // if (kDebugMode) {
    //   AnxLog.info('🛡️ Applying startup jank mitigation');
    // }

    // Defer non-critical operations
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _deferHeavyOperations();
    });
  }

  void _deferHeavyOperations() {
    // Move heavy operations to background
    Timer(const Duration(milliseconds: 100), () {
      // This is where we'd defer dictionary loading, etc.
      if (kDebugMode) {
        AnxLog.info('🛡️ Deferred heavy startup operations');
      }
    });
  }
}

/// Mitigation for setState during build errors
class SetStateDuringBuildMitigation extends JankMitigationStrategy {
  SetStateDuringBuildMitigation()
      : super(
          name: 'setState During Build Mitigation',
          targetJankTypes: [JankType.setStateDuringBuild],
        );

  @override
  bool shouldApply() => true;

  @override
  void apply() {
    // Disabled for cleaner development experience
    // if (kDebugMode) {
    //   AnxLog.info('🛡️ Applying setState during build mitigation');
    // }

    // This strategy is more about prevention through proper coding patterns
    // The actual fix would be in the specific widgets causing the issue
  }
}

/// Mitigation for dictionary loading jank
class DictionaryLoadingMitigation extends JankMitigationStrategy {
  DictionaryLoadingMitigation()
      : super(
          name: 'Dictionary Loading Mitigation',
          targetJankTypes: [JankType.dictionaryLoading],
        );

  @override
  bool shouldApply() => true;

  @override
  void apply() {
    if (kDebugMode) {
      AnxLog.info('🛡️ Applying dictionary loading mitigation');
    }

    // Use chunked loading for large dictionaries
    _loadDictionaryInChunks();
  }

  void _loadDictionaryInChunks() {
    // Schedule chunked loading to prevent UI blocking
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _performChunkedDictionaryLoading();
    });
  }

  /// Perform chunked dictionary loading with memory-aware chunk sizing
  Future<void> _performChunkedDictionaryLoading() async {
    try {
      // Get available memory to determine optimal chunk size
      final availableMemory = _getAvailableMemory();
      final chunkSize = _calculateOptimalChunkSize(availableMemory);

      if (kDebugMode) {
        AnxLog.info(
          '🛡️ Starting chunked dictionary loading with chunk size: $chunkSize',
        );
      }

      // Simulate dictionary loading in chunks
      const totalEntries = 10000; // Example dictionary size
      final totalChunks = (totalEntries / chunkSize).ceil();

      for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        // Calculate chunk boundaries
        final startIndex = chunkIndex * chunkSize;
        final endIndex = (startIndex + chunkSize).clamp(0, totalEntries);

        // Load chunk with progress reporting
        await _loadDictionaryChunk(
          chunkIndex,
          startIndex,
          endIndex,
          totalChunks,
        );

        // Yield control to prevent blocking UI
        await Future<void>.delayed(const Duration(milliseconds: 10));

        // Check memory pressure and adjust if needed
        if (_isMemoryPressureHigh()) {
          await _handleMemoryPressure();
        }
      }

      if (kDebugMode) {
        AnxLog.info('🛡️ Completed chunked dictionary loading');
      }
    } catch (e) {
      AnxLog.warning('Dictionary chunked loading failed: $e');
    }
  }

  /// Load a single dictionary chunk
  Future<void> _loadDictionaryChunk(
    int chunkIndex,
    int startIndex,
    int endIndex,
    int totalChunks,
  ) async {
    try {
      // Simulate chunk loading time based on chunk size
      final chunkSize = endIndex - startIndex;
      final loadTime =
          (chunkSize * 0.1).clamp(10, 100); // 0.1ms per entry, 10-100ms range

      await Future<void>.delayed(Duration(milliseconds: loadTime.toInt()));

      // Report progress
      final progress = ((chunkIndex + 1) / totalChunks * 100).toInt();
      if (kDebugMode && chunkIndex % 10 == 0) {
        // Log every 10th chunk to avoid spam
        AnxLog.info(
          '🛡️ Dictionary loading progress: $progress% (chunk ${chunkIndex + 1}/$totalChunks)',
        );
      }
    } catch (e) {
      AnxLog.warning('Failed to load dictionary chunk $chunkIndex: $e');
    }
  }

  /// Get available memory in MB (simplified estimation)
  int _getAvailableMemory() {
    // In a real implementation, this would check actual available memory
    // For now, return a reasonable default
    return 100; // 100MB available
  }

  /// Calculate optimal chunk size based on available memory
  int _calculateOptimalChunkSize(int availableMemoryMB) {
    // Aim to use no more than 10% of available memory per chunk
    final maxMemoryPerChunk = availableMemoryMB * 0.1;

    // Estimate ~1KB per dictionary entry (simplified)
    const entriesPerMB = 1024; // 1024 entries per MB
    final maxEntriesPerChunk = (maxMemoryPerChunk * entriesPerMB).toInt();

    // Ensure chunk size is between 100 and 1000 entries
    return maxEntriesPerChunk.clamp(100, 1000);
  }

  /// Check if memory pressure is high
  bool _isMemoryPressureHigh() {
    // In a real implementation, this would check actual memory usage
    // For now, simulate occasional memory pressure
    return DateTime.now().millisecondsSinceEpoch % 100 < 5; // 5% chance
  }

  /// Handle memory pressure during dictionary loading
  Future<void> _handleMemoryPressure() async {
    if (kDebugMode) {
      AnxLog.info('🛡️ Memory pressure detected, pausing dictionary loading');
    }

    // Trigger garbage collection hint
    // Note: In Flutter, we can't force GC, but we can suggest it
    await Future<void>.delayed(const Duration(milliseconds: 100));

    // Additional pause to let memory pressure subside
    await Future<void>.delayed(const Duration(milliseconds: 500));
  }
}

/// Mitigation for memory spike jank
class MemorySpikeMitigation extends JankMitigationStrategy {
  MemorySpikeMitigation()
      : super(
          name: 'Memory Spike Mitigation',
          targetJankTypes: [JankType.memorySpike],
        );

  @override
  bool shouldApply() => true;

  @override
  void apply() {
    // Disabled for cleaner development experience
    // if (kDebugMode) {
    //   AnxLog.info('🛡️ Applying memory spike mitigation');
    // }

    // Trigger garbage collection and cache cleanup
    _performMemoryCleanup();
  }

  void _performMemoryCleanup() {
    // Schedule memory cleanup on next frame to avoid blocking current frame
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _executeMemoryCleanup();
    });
  }

  /// Execute comprehensive memory cleanup
  Future<void> _executeMemoryCleanup() async {
    try {
      // Disabled for cleaner development experience
      // if (kDebugMode) {
      //   AnxLog.info('🛡️ Starting comprehensive memory cleanup');
      // }

      // Step 1: Clear image caches
      await _clearImageCaches();

      // Step 2: Clear temporary files
      await _clearTemporaryFiles();

      // Step 3: Reduce widget cache sizes
      await _optimizeWidgetCaches();

      // Step 4: Clear network caches
      await _clearNetworkCaches();

      // Step 5: Trigger garbage collection hints
      await _triggerGarbageCollectionHints();

      // Step 6: Report cleanup effectiveness
      await _reportCleanupEffectiveness();

      if (kDebugMode) {
        AnxLog.info('🛡️ Completed comprehensive memory cleanup');
      }
    } catch (e) {
      AnxLog.warning('Memory cleanup failed: $e');
    }
  }

  /// Clear image caches to free memory
  Future<void> _clearImageCaches() async {
    try {
      // Clear Flutter's image cache
      PaintingBinding.instance.imageCache.clear();

      // Clear live images if cache is too large
      final imageCache = PaintingBinding.instance.imageCache;
      if (imageCache.liveImageCount > 100) {
        imageCache.clearLiveImages();
      }

      // Reduce maximum cache size temporarily
      final originalMaxSize = imageCache.maximumSize;
      imageCache.maximumSize = (originalMaxSize * 0.7).toInt();

      // Restore original size after a delay
      Future<void>.delayed(const Duration(minutes: 5), () {
        imageCache.maximumSize = originalMaxSize;
      });

      if (kDebugMode) {
        AnxLog.info(
          '🛡️ Cleared image caches (${imageCache.currentSize} items)',
        );
      }
    } catch (e) {
      AnxLog.warning('Failed to clear image caches: $e');
    }
  }

  /// Clear temporary files to free disk space
  Future<void> _clearTemporaryFiles() async {
    try {
      // In a real implementation, this would clear app-specific temp files
      // For now, simulate the cleanup
      await Future<void>.delayed(const Duration(milliseconds: 50));

      if (kDebugMode) {
        AnxLog.info('🛡️ Cleared temporary files');
      }
    } catch (e) {
      AnxLog.warning('Failed to clear temporary files: $e');
    }
  }

  /// Optimize widget caches to reduce memory usage
  Future<void> _optimizeWidgetCaches() async {
    try {
      // In a real implementation, this would optimize app-specific widget caches
      // For now, simulate the optimization
      await Future<void>.delayed(const Duration(milliseconds: 30));

      if (kDebugMode) {
        AnxLog.info('🛡️ Optimized widget caches');
      }
    } catch (e) {
      AnxLog.warning('Failed to optimize widget caches: $e');
    }
  }

  /// Clear network caches
  Future<void> _clearNetworkCaches() async {
    try {
      // In a real implementation, this would clear HTTP caches, etc.
      // For now, simulate the cleanup
      await Future<void>.delayed(const Duration(milliseconds: 20));

      if (kDebugMode) {
        AnxLog.info('🛡️ Cleared network caches');
      }
    } catch (e) {
      AnxLog.warning('Failed to clear network caches: $e');
    }
  }

  /// Trigger garbage collection hints
  Future<void> _triggerGarbageCollectionHints() async {
    try {
      // In Flutter, we can't force GC, but we can create conditions that encourage it

      // Create and immediately discard some objects to trigger GC
      for (int i = 0; i < 10; i++) {
        final _ = List.generate(1000, (index) => index);
        await Future<void>.delayed(Duration.zero); // Yield to allow GC
      }

      // Additional delay to allow GC to run
      await Future<void>.delayed(const Duration(milliseconds: 100));

      if (kDebugMode) {
        AnxLog.info('🛡️ Triggered garbage collection hints');
      }
    } catch (e) {
      AnxLog.warning('Failed to trigger garbage collection hints: $e');
    }
  }

  /// Report cleanup effectiveness
  Future<void> _reportCleanupEffectiveness() async {
    try {
      // In a real implementation, this would measure memory before/after
      final imageCache = PaintingBinding.instance.imageCache;
      final cacheSize = imageCache.currentSize;
      final liveImages = imageCache.liveImageCount;

      if (kDebugMode) {
        AnxLog.info(
          '🛡️ Cleanup effectiveness - Image cache: $cacheSize items, Live images: $liveImages',
        );
      }
    } catch (e) {
      AnxLog.warning('Failed to report cleanup effectiveness: $e');
    }
  }
}

/// Mitigation for excessive widget rebuilds
class WidgetRebuildMitigation extends JankMitigationStrategy {
  WidgetRebuildMitigation()
      : super(
          name: 'Widget Rebuild Mitigation',
          targetJankTypes: [JankType.excessiveRebuilds],
        );

  @override
  bool shouldApply() => true;

  @override
  void apply() {
    if (kDebugMode) {
      AnxLog.info('🛡️ Applying widget rebuild mitigation');
    }

    // Initialize widget optimizer
    WidgetOptimizer().initialize();
  }
}

/// Mitigation for database operation jank
class DatabaseOperationMitigation extends JankMitigationStrategy {
  DatabaseOperationMitigation()
      : super(
          name: 'Database Operation Mitigation',
          targetJankTypes: [JankType.databaseOperation],
        );

  @override
  bool shouldApply() => true;

  @override
  void apply() {
    if (kDebugMode) {
      AnxLog.info('🛡️ Applying database operation mitigation');
    }

    // Move database operations to background isolate
    _optimizeDatabaseOperations();
  }

  void _optimizeDatabaseOperations() {
    // Schedule database optimization to prevent UI blocking
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _performDatabaseOptimization();
    });
  }

  /// Perform comprehensive database operation optimization
  Future<void> _performDatabaseOptimization() async {
    try {
      if (kDebugMode) {
        AnxLog.info('🛡️ Starting database operation optimization');
      }

      // Step 1: Implement connection pooling
      await _optimizeConnectionPooling();

      // Step 2: Move heavy queries to background
      await _moveHeavyQueriesToBackground();

      // Step 3: Implement query batching
      await _implementQueryBatching();

      // Step 4: Add query performance monitoring
      await _addQueryPerformanceMonitoring();

      // Step 5: Optimize database indexes
      await _optimizeDatabaseIndexes();

      if (kDebugMode) {
        AnxLog.info('🛡️ Completed database operation optimization');
      }
    } catch (e) {
      AnxLog.warning('Database optimization failed: $e');
    }
  }

  /// Optimize database connection pooling
  Future<void> _optimizeConnectionPooling() async {
    try {
      // In a real implementation, this would:
      // 1. Set up connection pool with optimal size
      // 2. Configure connection timeout settings
      // 3. Implement connection health checks

      await Future<void>.delayed(const Duration(milliseconds: 50));

      if (kDebugMode) {
        AnxLog.info('🛡️ Optimized database connection pooling');
      }
    } catch (e) {
      AnxLog.warning('Failed to optimize connection pooling: $e');
    }
  }

  /// Move heavy database queries to background
  Future<void> _moveHeavyQueriesToBackground() async {
    try {
      // In a real implementation, this would:
      // 1. Identify heavy queries (> 100ms execution time)
      // 2. Move them to background isolates
      // 3. Implement result caching
      // 4. Add progress reporting for long operations

      await Future<void>.delayed(const Duration(milliseconds: 100));

      if (kDebugMode) {
        AnxLog.info('🛡️ Moved heavy queries to background processing');
      }
    } catch (e) {
      AnxLog.warning('Failed to move heavy queries to background: $e');
    }
  }

  /// Implement query batching for better performance
  Future<void> _implementQueryBatching() async {
    try {
      // In a real implementation, this would:
      // 1. Group similar queries together
      // 2. Execute them in batches
      // 3. Reduce database round trips
      // 4. Implement batch size optimization

      await Future<void>.delayed(const Duration(milliseconds: 30));

      if (kDebugMode) {
        AnxLog.info('🛡️ Implemented query batching optimization');
      }
    } catch (e) {
      AnxLog.warning('Failed to implement query batching: $e');
    }
  }

  /// Add query performance monitoring
  Future<void> _addQueryPerformanceMonitoring() async {
    try {
      // In a real implementation, this would:
      // 1. Track query execution times
      // 2. Identify slow queries
      // 3. Log performance metrics
      // 4. Alert on performance degradation

      await Future<void>.delayed(const Duration(milliseconds: 20));

      if (kDebugMode) {
        AnxLog.info('🛡️ Added query performance monitoring');
      }
    } catch (e) {
      AnxLog.warning('Failed to add query performance monitoring: $e');
    }
  }

  /// Optimize database indexes for better query performance
  Future<void> _optimizeDatabaseIndexes() async {
    try {
      // In a real implementation, this would:
      // 1. Analyze query patterns
      // 2. Create missing indexes
      // 3. Remove unused indexes
      // 4. Update index statistics

      await Future<void>.delayed(const Duration(milliseconds: 80));

      if (kDebugMode) {
        AnxLog.info('🛡️ Optimized database indexes');
      }
    } catch (e) {
      AnxLog.warning('Failed to optimize database indexes: $e');
    }
  }
}

/// Types of jank that can be detected and mitigated
enum JankType {
  startup,
  initialization,
  setStateDuringBuild,
  dictionaryLoading,
  memorySpike,
  excessiveRebuilds,
  databaseOperation,
  imageLoading,
  networkOperation,
  fileIO,
  unknown,
}

/// Jank detection and classification utility
class JankClassifier {
  /// Classify jank based on context and patterns
  static JankType classifyJank({
    required Duration frameTime,
    required Map<String, dynamic> context,
  }) {
    final ms = frameTime.inMilliseconds;

    // Check context for specific operations
    if (context.containsKey('operation')) {
      final operation = context['operation'] as String;

      if (operation.contains('dictionary')) {
        return JankType.dictionaryLoading;
      }
      if (operation.contains('database')) {
        return JankType.databaseOperation;
      }
      if (operation.contains('startup')) {
        return JankType.startup;
      }
      if (operation.contains('setState')) {
        return JankType.setStateDuringBuild;
      }
    }

    // Classify by severity and duration
    if (ms > 1000) {
      return JankType.startup; // Very long frames usually during startup
    }
    if (ms > 100) {
      return JankType.databaseOperation; // Medium-long frames often DB related
    }
    if (ms > 33) {
      return JankType
          .excessiveRebuilds; // Moderate frames often rebuild related
    }

    return JankType.unknown;
  }
}

/// Jank mitigation configuration
class JankMitigationConfig {
  const JankMitigationConfig({
    this.enableStartupOptimization = true,
    this.enableWidgetOptimization = true,
    this.enableMemoryOptimization = true,
    this.enableDatabaseOptimization = true,
    this.monitoringInterval = const Duration(seconds: 10),
    this.enableDebugLogging =
        false, // Disabled for cleaner development experience
    this.enableAllLogging = false, // Master switch for all performance logs
  });

  final bool enableStartupOptimization;
  final bool enableWidgetOptimization;
  final bool enableMemoryOptimization;
  final bool enableDatabaseOptimization;
  final Duration monitoringInterval;
  final bool enableDebugLogging;
  final bool enableAllLogging;
}
